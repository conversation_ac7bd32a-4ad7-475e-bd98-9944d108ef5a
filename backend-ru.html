<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Резюме — Зобнин Андрей</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #2563eb;
      --primary-light: #dbeafe;
      --primary-medium: #93c5fd;
      --icon-bg: #1e3a8a;
      --secondary-color: #334155;
      --text-color: #1e293b;
      --light-gray: #94a3b8;
      --border-color: #e2e8f0;
      --background: #f8fafc;
      --card-bg: #ffffff;
      --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', Arial, sans-serif;
      background-color: var(--background);
      color: var(--text-color);
      line-height: 1.6;
      max-width: 900px;
      margin: 0 auto;
      padding: 40px;
    }

    .resume-container {
      background-color: var(--card-bg);
      border-radius: 12px;
      box-shadow: var(--shadow);
      overflow: hidden;
      margin-bottom: 40px;
    }

    header {
      display: flex;
      align-items: center;
      padding: 30px;
      background: linear-gradient(135deg, var(--primary-color), #1e3a8a);
      color: white;
      position: relative;
    }

    .profile-image {
      position: relative;
      margin-right: 40px;
    }

    img {
      border-radius: 50%;
      width: 160px;
      height: 160px;
      object-fit: cover;
      border: 4px solid white;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .header-content h1 {
      margin: 0;
      font-family: 'Montserrat', sans-serif;
      font-size: 36px;
      font-weight: 700;
      letter-spacing: 0.5px;
    }

    .header-content h2 {
      font-family: 'Montserrat', sans-serif;
      font-size: 15px;
      font-weight: 400;
      opacity: 0.9;
      border: none;
      padding: 0;
      margin-bottom: 10px;
    }

    .main-content {
      padding: 25px;
    }

    section {
      margin-bottom: 20px;
    }

    section:last-child {
      margin-bottom: 0;
    }

    h2.section-title {
      font-family: 'Montserrat', sans-serif;
      font-size: 20px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--border-color);
      position: relative;
    }

    h2.section-title::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 60px;
      height: 2px;
      background-color: var(--primary-color);
    }

    p {
      margin-bottom: 10px;
      color: var(--text-color);
      font-size: 14px;
    }

    .contacts-grid {
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;
      margin-top: 30px;
      margin-bottom: 15px;
    }

    .contact-item {
      display: flex;
      align-items: center;
    }

    .contact-icon {
      width: 30px;
      height: 30px;
      background-color: var(--icon-bg);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 6px;
      color: var(--primary-color);
      text-align: center;
      line-height: 1;
      position: relative;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .contact-icon span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 16px;
    }

    header .contacts a {
      color: white;
      text-decoration: none;
      font-weight: 600;
      font-size: 14px;
    }

    .main-content a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .skills-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 10px;
    }

    .skill {
      background-color: var(--primary-light);
      color: var(--primary-color);
      border-radius: 20px;
      padding: 5px 12px;
      font-size: 13px;
      font-weight: 500;
    }

    .job {
      margin-bottom: 15px;
      padding: 12px;
      border-radius: 8px;
      background-color: #f8fafc;
      border-left: 3px solid var(--primary-color);
    }

    .job:last-child {
      margin-bottom: 0;
    }

    .job-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
      flex-wrap: wrap;
    }

    .job-title {
      font-weight: 600;
      font-size: 16px;
      color: var(--secondary-color);
      margin-right: 15px;
    }

    .job-period {
      color: var(--light-gray);
      font-style: italic;
      font-size: 14px;
      background-color: #f1f5f9;
      padding: 4px 10px;
      border-radius: 12px;
    }

    .job-description {
      margin-top: 10px;
    }

    .job-description p {
      margin-bottom: 12px;
      line-height: 1.5;
    }

    .job-description ul {
      list-style-type: none;
      padding-left: 0;
    }

    .job-description li {
      position: relative;
      padding-left: 15px;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .job-description li::before {
      content: "•";
      color: var(--primary-color);
      font-weight: bold;
      position: absolute;
      left: 0;
    }

    .job-link {
      display: inline-block;
      margin-top: 10px;
      color: var(--primary-color);
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
    }

    .language-item {
      display: inline-block;
      margin-right: 20px;
      background-color: var(--primary-light);
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
    }

    .language-name {
      color: var(--secondary-color);
      font-weight: 600;
    }

    .language-level {
      color: var(--primary-color);
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <header>
      <div class="profile-image">
        <img src="data:image/jpeg;base64,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" alt="Фото">
      </div>
      <div class="header-content">
        <h1>Зобнин Андрей</h1>
        <h2>Fullstack-разработчик с 10+ лет опыта, специализация Backend (PHP, Laravel, MySQL)</h2>
        <div class="contacts">
          <div class="contacts-grid">
            <div class="contact-item">
              <div class="contact-icon"><span>📍</span></div>
              <a href="#">Вьетнам</a>
            </div>
            <div class="contact-item">
              <div class="contact-icon"><span>💬</span></div>
              <a href="https://t.me/fiter92">@fiter92</a>
            </div>
            <div class="contact-item">
              <div class="contact-icon"><span>✉️</span></div>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            <div class="contact-item">
              <div class="contact-icon"><span>👨‍💼</span></div>
              <a href="https://linkedin.com/in/fiter">linkedin.com/in/fiter</a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="main-content">
      <section>
        <h2 class="section-title">Профиль</h2>
        <p>
          Backend-разработчик с более чем 10-летним опытом создания серверных решений и API. Специализируюсь на разработке высоконагруженных веб-приложений на PHP и Laravel, проектировании архитектуры баз данных MySQL, создании RESTful API и микросервисов. Опыт работы с очередями, кэшированием (Redis), автоматизацией CI/CD и контейнеризацией (Docker). Имею опыт интеграции с внешними сервисами, платёжными системами и блокчейн-решениями. Эффективно работаю в международных командах, превращая бизнес-требования в масштабируемые технические решения.
        </p>
      </section>

      <section>
        <h2 class="section-title">Технические навыки</h2>
        <div class="skills-container">
          <span class="skill">PHP</span>
          <span class="skill">Laravel</span>
          <span class="skill">MySQL</span>
          <span class="skill">PostgreSQL</span>
          <span class="skill">REST API</span>
          <span class="skill">GraphQL</span>
          <span class="skill">Redis</span>
          <span class="skill">Queue</span>
          <span class="skill">Elasticsearch</span>
          <span class="skill">Docker</span>
          <span class="skill">AWS</span>
          <span class="skill">Linux</span>
          <span class="skill">Git</span>
          <span class="skill">PHPUnit</span>
          <span class="skill">Composer</span>
          <span class="skill">Nginx</span>
          <span class="skill">Apache</span>
          <span class="skill">Microservices</span>
          <span class="skill">CI/CD</span>
          <span class="skill">Wordpress</span>
          <span class="skill">Woocommerce</span>
          <span class="skill">JavaScript</span>
          <span class="skill">VueJS</span>
        </div>
      </section>

      <section>
        <h2 class="section-title">Опыт работы</h2>

        <div class="job">
          <div class="job-header">
            <div class="job-title">Centreville Tech LLC</div>
            <div class="job-period">2020 — 2024</div>
          </div>
          <div class="job-position">Backend-разработчик</div>
          <div class="job-description">
            <p>Работал в международной команде, разрабатывая серверную часть веб-приложений и API для клиентов из США и Европы. Отвечал за проектирование архитектуры баз данных, создание масштабируемых backend-решений и интеграцию с внешними сервисами. Активно взаимодействовал с англоязычными клиентами, участвовал в техническом планировании и code review.</p>
            <p><strong>Ключевые задачи:</strong></p>
            <ul>
              <li>Разработка RESTful API и GraphQL на Laravel с использованием современных паттернов</li>
              <li>Проектирование и оптимизация структуры баз данных MySQL и PostgreSQL</li>
              <li>Создание и поддержка backend-логики для интернет-магазинов на WooCommerce</li>
              <li>Разработка кастомных плагинов и API endpoints для WordPress</li>
              <li>Интеграция с платёжными системами (Stripe, PayPal) и внешними API</li>
              <li>Реализация системы очередей (Laravel Queue) и фоновых задач</li>
              <li>Настройка кэширования (Redis) и оптимизация производительности запросов</li>
              <li>Развёртывание и поддержка серверной инфраструктуры (AWS, Docker, Linux)</li>
              <li>Настройка CI/CD пайплайнов и автоматизация деплоя</li>
              <li>Написание unit и integration тестов с использованием PHPUnit</li>
              <li>Техническая коммуникация с англоязычными клиентами и командой</li>
            </ul>
            <a href="https://centrevilletech.com" class="job-link">centrevilletech.com</a>
          </div>
        </div>

        <div class="job">
          <div class="job-header">
            <div class="job-title">most.fan</div>
            <div class="job-period">2021 — 2023</div>
          </div>
          <div class="job-position">Backend-разработчик (part-time)</div>
          <div class="job-description">
            <p>Работал part-time в крипто-стартапе в сфере Web3, специализирующемся на токенизации знаменитостей. Отвечал за разработку серверной части платформы, интеграцию с блокчейн-сетями и создание API для работы с криптовалютными транзакциями.</p>
            <p><strong>Ключевые задачи:</strong></p>
            <ul>
              <li>Разработка RESTful API на Laravel для работы с токенами и NFT</li>
              <li>Интеграция с блокчейн-сетями (Ethereum, Polygon) через Web3 провайдеры</li>
              <li>Создание системы мониторинга транзакций и обработки событий блокчейна</li>
              <li>Разработка микросервисной архитектуры для обработки криптовалютных операций</li>
              <li>Реализация системы аутентификации и авторизации с использованием JWT</li>
              <li>Настройка очередей для асинхронной обработки блокчейн-транзакций</li>
              <li>Создание системы кэширования для оптимизации запросов к блокчейну</li>
              <li>Разработка административных API для управления платформой</li>
              <li>Интеграция с внешними криптовалютными биржами и кошельками</li>
            </ul>
          </div>
        </div>

        <div class="job">
          <div class="job-header">
            <div class="job-title">ООО «Прогрессивные Веб Технологии»</div>
            <div class="job-period">2016 — 2020</div>
          </div>
          <div class="job-position">Backend-разработчик</div>
          <div class="job-description">
            <p>Разрабатывал серверную часть корпоративных сайтов, интернет-магазинов и внутренних систем для клиентов компании. Работал с фреймворком Laravel, создавал кастомные API и интеграции. Отвечал за архитектуру баз данных, серверную логику и техническую оптимизацию. Руководил командой разработчиков и администрировал серверную инфраструктуру.</p>
            <p><strong>Ключевые задачи:</strong></p>
            <ul>
              <li>Разработка backend-логики на PHP и Laravel для корпоративных проектов</li>
              <li>Создание и поддержка API для сайтов на WordPress и 1С-Битрикс</li>
              <li>Разработка серверной части SPA-приложений с использованием Laravel</li>
              <li>Проектирование и оптимизация структуры баз данных MySQL</li>
              <li>Создание интеграций с API сторонних сервисов, платёжных систем и 1С</li>
              <li>Оптимизация производительности серверной части и SQL-запросов</li>
              <li>Администрирование VPS серверов и настройка веб-серверов (Apache, Nginx)</li>
              <li>Управление командой разработчиков и code review</li>
              <li>Настройка систем мониторинга и логирования</li>
            </ul>
            <a href="https://21group.ru" class="job-link">21group.ru</a>
          </div>
        </div>

        <div class="job">
          <div class="job-header">
            <div class="job-title">Фриланс</div>
            <div class="job-period">2012 — 2016</div>
          </div>
          <div class="job-position">PHP-разработчик</div>
          <div class="job-description">
            <p>Работал как независимый разработчик, специализируясь на создании серверных решений на PHP и популярных CMS. Разрабатывал backend-логику, API интеграции и оптимизировал производительность веб-приложений. Настраивал серверную инфраструктуру и базы данных.</p>
            <p><strong>Ключевые задачи:</strong></p>
            <ul>
              <li>Разработка серверной логики на чистом PHP и фреймворке Laravel</li>
              <li>Создание кастомных плагинов и API для WordPress, Joomla, 1C-Битрикс</li>
              <li>Разработка backend-части интернет-магазинов и корпоративных сайтов</li>
              <li>Проектирование и настройка баз данных MySQL, оптимизация SQL-запросов</li>
              <li>Создание RESTful API для интеграции с внешними сервисами</li>
              <li>Интеграция с платёжными системами, CRM и другими внешними API</li>
              <li>Оптимизация производительности серверной части и кэширование</li>
              <li>Настройка и администрирование веб-серверов (Apache, Nginx)</li>
            </ul>
            <div class="job-links">
              <a href="https://freelance.ru/fiter" class="job-link">freelance.ru/fiter</a>,
              <a href="https://www.upwork.com/freelancers/~015212b734d7587809" class="job-link">Upwork</a>
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2 class="section-title">Языки</h2>
        <div class="languages">
          <span class="language-item">
            <span class="language-name">Русский</span> —
            <span class="language-level">родной</span>
          </span>
          <span class="language-item">
            <span class="language-name">Английский</span> —
            <span class="language-level">B2</span>
          </span>
        </div>
      </section>
    </div>
  </div>
</body>
</html>
